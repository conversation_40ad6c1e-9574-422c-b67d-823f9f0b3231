﻿include(FetchContent)
set(<PERSON>SONCPP_VERSION_MAJOR 1)
set(JSONCPP_VERSION_MINOR 4)
set(JSONCPP_VERSION_PATCH 2)

FetchContent_Declare(
  jsoncpp
  URL ${AIDI_DEPS_ROOT}/jsoncpp/jsoncpp-${JSONCPP_VERSION_MAJOR}.${JSONCPP_VERSION_MINOR}.${JSONCPP_VERSION_PATCH}.zip
  UPDATE_DISCONNECTED
)
set(JSONCPP_WITH_TESTS OFF CACHE INTERNAL "donot build test of jsoncpp")

#FetchContent_MakeAvailable(jsoncpp)
if(NOT jsoncpp_POPULATED)
  FetchContent_Populate(jsoncpp)
  add_subdirectory(${jsoncpp_SOURCE_DIR} ${jsoncpp_BINARY_DIR} EXCLUDE_FROM_ALL)
  add_library(jsoncpp_static ALIAS jsoncpp_lib_static)
endif()
